<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصفحات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .test-result {
            padding: 10px;
            border-radius: 8px;
            margin: 5px 0;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="test-card">
                    <h1 class="text-center mb-4">
                        <i class="bi bi-clipboard-check me-2"></i>
                        اختبار نظام الصفحات الشامل
                    </h1>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h3><i class="bi bi-list-check me-2"></i>اختبار عناصر HTML</h3>
                            <div id="htmlTests"></div>
                        </div>
                        <div class="col-md-6">
                            <h3><i class="bi bi-gear me-2"></i>اختبار الوظائف JavaScript</h3>
                            <div id="jsTests"></div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h3><i class="bi bi-graph-up me-2"></i>ملخص النتائج</h3>
                            <div id="summary"></div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button class="btn btn-primary btn-lg" onclick="runTests()">
                            <i class="bi bi-play-circle me-2"></i>تشغيل الاختبارات
                        </button>
                        <a href="index.html" class="btn btn-success btn-lg ms-2">
                            <i class="bi bi-house me-2"></i>العودة للنظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function runTests() {
            const htmlTests = document.getElementById('htmlTests');
            const jsTests = document.getElementById('jsTests');
            const summary = document.getElementById('summary');
            
            let passCount = 0;
            let failCount = 0;
            
            // اختبار عناصر HTML
            const paginationElements = [
                'productsPagination',
                'inventoryPagination',
                'workerWithdrawalsPagination',
                'movementsPagination',
                'workersPagination',
                'attendancePagination',
                'payrollPagination',
                'materialsPagination',
                'categoriesPagination',
                'usersPagination'
            ];
            
            htmlTests.innerHTML = '';
            paginationElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                const result = document.createElement('div');
                
                if (element) {
                    result.className = 'test-result test-pass';
                    result.innerHTML = `<i class="bi bi-check-circle me-2"></i>عنصر ${elementId} موجود`;
                    passCount++;
                } else {
                    result.className = 'test-result test-fail';
                    result.innerHTML = `<i class="bi bi-x-circle me-2"></i>عنصر ${elementId} مفقود`;
                    failCount++;
                }
                
                htmlTests.appendChild(result);
            });
            
            // اختبار الوظائف JavaScript
            const functions = [
                'goToProductsPage',
                'goToInventoryPage',
                'goToWithdrawalsPage',
                'goToMovementsPage',
                'goToWorkersPage',
                'goToAttendancePage',
                'goToPayrollPage',
                'goToMaterialsPage',
                'goToCategoriesPage',
                'goToUsersPage'
            ];
            
            jsTests.innerHTML = '';
            functions.forEach(funcName => {
                const result = document.createElement('div');
                
                if (typeof window[funcName] === 'function') {
                    result.className = 'test-result test-pass';
                    result.innerHTML = `<i class="bi bi-check-circle me-2"></i>وظيفة ${funcName} متاحة`;
                    passCount++;
                } else {
                    result.className = 'test-result test-fail';
                    result.innerHTML = `<i class="bi bi-x-circle me-2"></i>وظيفة ${funcName} مفقودة`;
                    failCount++;
                }
                
                jsTests.appendChild(result);
            });
            
            // ملخص النتائج
            const totalTests = passCount + failCount;
            const successRate = Math.round((passCount / totalTests) * 100);
            
            summary.innerHTML = `
                <div class="test-result test-info">
                    <h5><i class="bi bi-info-circle me-2"></i>ملخص الاختبارات</h5>
                    <p><strong>إجمالي الاختبارات:</strong> ${totalTests}</p>
                    <p><strong>نجح:</strong> ${passCount}</p>
                    <p><strong>فشل:</strong> ${failCount}</p>
                    <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                </div>
            `;
            
            if (successRate === 100) {
                summary.innerHTML += `
                    <div class="test-result test-pass mt-2">
                        <h5><i class="bi bi-trophy me-2"></i>تهانينا!</h5>
                        <p>جميع اختبارات نظام الصفحات نجحت بنسبة 100%</p>
                    </div>
                `;
            }
        }
        
        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.onload = function() {
            // انتظار قليل للتأكد من تحميل جميع العناصر
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
